"""
Orchestrator Agent - Central Coordinator and State Manager

The Orchestrator is the central nervous system of the multi-agent conversation assistant.
It manages conversation state, routes messages between agents, and controls the overall flow.

Key Responsibilities:
1. Manage conversation macro states (AI_SPEAKING, USER_SPEAKING, etc.)
2. Route messages between all agents
3. Control Speaker agent start/stop (exclusive authority)
4. Handle interruption decisions from Decider
5. Maintain session state and conversation history
"""

import asyncio
import uuid
from typing import Dict, Optional, Any, List, Callable
from datetime import datetime
from enum import Enum
from loguru import logger

from core.message_bus import message_bus, TopicNames, create_message
from core.event_types import (
    EventType,
    MessageType,
    AgentState,
    BaseMessage,
    InterruptionMessage,
    OrchstratorCommand,
)
from core.config import settings
from core.base_agent import BaseAgent
from core.vad_manager import get_vad_manager, VADPreset


class ConversationState(str, Enum):
    """
    Macro states for conversation management.
    """

    IDLE = "idle"
    USER_SPEAKING = "user_speaking"
    AI_THINKING = "ai_thinking"
    AI_SPEAKING = "ai_speaking"
    INTERRUPTION_PENDING = "interruption_pending"
    ERROR = "error"


class Session:
    """
    Represents a conversation session with state and history.
    """

    def __init__(
        self, session_id: str, send_callback: Optional[Callable] = None
    ):  # 🎯 改造
        self.session_id = session_id
        self.created_at = datetime.utcnow()
        self.state = ConversationState.IDLE
        self.conversation_history: List[Dict[str, Any]] = []
        self.current_speaker_task: Optional[str] = None
        self.last_user_input: Optional[str] = None
        self.interruption_count = 0
        self.metadata: Dict[str, Any] = {"voice": "alloy"}
        self.send_callback = send_callback  # 🎯 存储回调
        # 🎯 废弃pending_interruption_response，改用更明确的意图标记
        self.interruption_intent: Optional[Dict[str, Any]] = None

    def add_to_history(
        self, role: str, content: str, timestamp: Optional[datetime] = None
    ):
        """Add entry to conversation history."""
        entry = {
            "role": role,
            "content": content,
            "timestamp": timestamp or datetime.utcnow(),
            "session_id": self.session_id,
        }
        self.conversation_history.append(entry)

    def get_recent_history(self, count: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history."""
        return self.conversation_history[-count:] if self.conversation_history else []


class Orchestrator(BaseAgent):
    """
    Central Orchestrator Agent implementing the Dispider architecture coordinator.
    """

    def __init__(self):
        super().__init__("orchestrator")
        self.agent_id = "orchestrator"
        self.sessions: Dict[str, Session] = {}
        self.active_session_id: Optional[str] = None

        # Agent status tracking
        self.agent_states: Dict[str, AgentState] = {
            "listener": AgentState.IDLE,
            "tactical_thinker": AgentState.IDLE,
            "strategic_thinker": AgentState.IDLE,
            "decider": AgentState.IDLE,
            "speaker": AgentState.IDLE,
        }

        # Message routing configuration
        self.message_routes: Dict[str, List[str]] = {
            # Define which agents should receive which types of messages
            "audio_input": ["listener"],
            "transcript": ["tactical_thinker", "decider"],
            "tactical_analysis": ["decider", "strategic_thinker"],
            "interruption_decision": ["speaker"],
            "tts_command": ["speaker"],
        }

    async def initialize(self) -> bool:
        """
        Initialize the Orchestrator agent.
        """
        try:
            # Connect to message bus
            if not await message_bus.connect():
                self.logger.error("Failed to connect to message bus")
                return False

            # Subscribe to relevant topics
            await self._setup_subscriptions()

            self.logger.info("Orchestrator initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Orchestrator: {e}")
            return False

    async def start(self) -> bool:
        """
        Start the Orchestrator agent.
        """
        try:
            self.logger.info("Starting Orchestrator agent...")

            # Start message bus listening
            await message_bus.start_listening()

            # Notify system that orchestrator is online
            await self._publish_status_update()

            # 设置运行状态标志
            self.is_running = True

            self.logger.info("Orchestrator agent started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start Orchestrator: {e}")
            self.is_running = False
            return False

    async def shutdown(self) -> None:
        """
        Shutdown the Orchestrator agent and clean up resources.
        """
        try:
            self.logger.info("Shutting down Orchestrator agent...")

            # Stop any active speaker tasks
            await self._stop_all_speaker_tasks()

            # Disconnect from message bus
            await message_bus.stop_listening()
            await message_bus.disconnect()

            self.logger.info("Orchestrator shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during Orchestrator shutdown: {e}")

    async def stop(self):
        """
        Stop the Orchestrator agent (legacy method for compatibility).
        """
        await self.shutdown()

        logger.info("Orchestrator agent stopped")

    # 🎯 改造 create_session 方法
    async def create_session(
        self, session_id: Optional[str] = None, send_callback: Optional[Callable] = None
    ) -> str:
        """
        Create a new conversation session.

        Args:
            session_id: Optional session ID, generates one if not provided
            send_callback: Optional direct send callback for critical messages

        Returns:
            Session ID
        """
        if not session_id:
            session_id = str(uuid.uuid4())

        session = Session(session_id, send_callback=send_callback)  # 🎯 传递回调
        self.sessions[session_id] = session
        self.active_session_id = session_id

        # 🎯 新增：初始化为STANDARD VAD模式
        vad_manager = get_vad_manager()
        if vad_manager:
            await vad_manager.switch_to_standard()
            logger.info("🎯 Initialized session with STANDARD VAD mode")

        # Publish session creation event
        await message_bus.publish(
            TopicNames.SESSION_EVENTS,
            create_message(
                source_agent=self.agent_id,
                event_type=EventType.SESSION_CREATED,
                message_type=MessageType.ORCHESTRATOR_STATUS,
                data={
                    "session_id": session_id,
                    "timestamp": session.created_at.isoformat(),
                },
            ),
        )

        logger.info(
            f"Created new session: {session_id} with a {'direct' if send_callback else 'standard'} send channel."
        )
        return session_id

    async def end_session(self, session_id: str):
        """
        End a conversation session.

        Args:
            session_id: Session to end
        """
        if session_id not in self.sessions:
            logger.warning(f"Session {session_id} not found")
            return

        session = self.sessions[session_id]

        # Stop any active speaker tasks for this session
        if session.current_speaker_task:
            await self._stop_speaker_task(session.current_speaker_task)

        # Update session state
        session.state = ConversationState.IDLE

        # Remove from active sessions if it's the current one
        if self.active_session_id == session_id:
            self.active_session_id = None

        # Publish session end event
        await message_bus.publish(
            TopicNames.SESSION_EVENTS,
            create_message(
                source_agent=self.agent_id,
                event_type=EventType.SESSION_ENDED,
                message_type=MessageType.ORCHESTRATOR_STATUS,
                data={"session_id": session_id},
            ),
        )

        # Clean up session (keep for potential analysis)
        # del self.sessions[session_id]

        logger.info(f"Ended session: {session_id}")

    async def handle_user_speech_start(self, data: Dict[str, Any]):
        """
        Handle user speech start event with fast-track barge-in logic.
        处理用户开始说话事件，实现快速通道barge-in逻辑
        """
        session_id = data.get("session_id") or self.active_session_id
        if not session_id or session_id not in self.sessions:
            logger.warning("No active session for user speech")
            return

        session = self.sessions[session_id]

        # 🎯 快速通道逻辑：AI正在说话时用户插话，立即执行barge-in
        if session.state == ConversationState.AI_SPEAKING:
            logger.info(
                "⚡️ Barge-in detected! User started speaking while AI was responding. Executing immediate interruption."
            )
            session.state = ConversationState.INTERRUPTION_PENDING

            # 🎯 新增：切换到BARGE_IN VAD模式以优化打断检测
            vad_manager = get_vad_manager()
            if vad_manager:
                await vad_manager.switch_to_barge_in()
                logger.info(
                    "🎯 Switched to BARGE_IN VAD mode for optimized interruption detection"
                )

            # 创建barge-in决策对象，绕过Decider分析链条
            barge_in_decision = {
                "type": "barge_in",
                "reason": "User started speaking (barge-in)",
                "confidence": 1.0,
                "suggested_response": None,  # Barge-in通常不需要AI立即回复
                "timing": "immediate",
            }

            # 直接执行打断，不等待Decider
            await self._execute_interruption(session, "barge_in", None, "User barge-in")

            logger.info(f"🎯 Fast-track barge-in executed for session {session_id}")

        else:
            # 正常的用户开始说话流程
            session.state = ConversationState.USER_SPEAKING
            logger.info("User started speaking")

        await self._publish_status_update()

    async def handle_user_speech_end(self, data: Dict[str, Any]):
        """
        Handle user speech end event.
        """
        session_id = data.get("session_id") or self.active_session_id
        if not session_id or session_id not in self.sessions:
            return

        session = self.sessions[session_id]
        session.state = ConversationState.AI_THINKING

        # 🎯 新增：用户停止说话时切换回STANDARD VAD模式
        vad_manager = get_vad_manager()
        if vad_manager:
            await vad_manager.switch_to_standard()
            logger.info("🎯 Switched back to STANDARD VAD mode")

        # Store user input if available
        if "transcript" in data:
            session.last_user_input = data["transcript"]
            session.add_to_history("user", data["transcript"])

        logger.info("User finished speaking - AI thinking")
        await self._publish_status_update()

    async def handle_interruption_decision(
        self, topic: str, message_data: Dict[str, Any]
    ):
        """
        Handle interruption decision from Decider agent.
        This is a critical function that executes the main interruption logic.
        """
        try:
            session_id = message_data.get("data", {}).get(
                "session_id", self.active_session_id
            )
            if not session_id or session_id not in self.sessions:
                logger.warning("No valid session for interruption decision")
                return

            session = self.sessions[session_id]
            interruption_data = message_data.get("data", {})

            # 修复：正确解析decider发送的decision对象
            decision = interruption_data.get("decision", {})
            should_interrupt = bool(decision)  # 如果有decision对象就说明要打断
            interruption_type = decision.get("type")
            confidence = decision.get("confidence", 0.0)
            suggested_response = decision.get("suggested_response")
            reason = decision.get("reason", "Unknown")

            logger.info(
                f"Interruption decision: {should_interrupt} (type: {interruption_type}, confidence: {confidence}, reason: {reason})"
            )

            if should_interrupt:
                # --- START OF FIX ---
                # 核心修复：不再设置意图并等待，而是立即执行打断
                # 移除了所有 session.interruption_intent 相关的逻辑

                logger.info(
                    f"✅ Immediate execution of interruption for session {session_id}."
                )
                await self._execute_interruption(
                    session, interruption_type, suggested_response, reason
                )
                # --- END OF FIX ---
            else:
                # Continue normal flow
                session.state = ConversationState.USER_SPEAKING
                await self._publish_status_update()

        except Exception as e:
            logger.error(f"Error handling interruption decision: {e}")

    async def handle_response_required(self, topic: str, message_data: Dict[str, Any]):
        """
        Handle response generation request.
        """
        session_id = message_data.get("data", {}).get(
            "session_id", self.active_session_id
        )
        if not session_id or session_id not in self.sessions:
            return

        session = self.sessions[session_id]

        # Request strategic thinking for response generation
        await message_bus.publish(
            TopicNames.STRATEGIC_ANALYSIS,
            create_message(
                source_agent=self.agent_id,
                event_type=EventType.RESPONSE_REQUIRED,
                message_type=MessageType.ORCHESTRATOR_COMMAND,
                data={
                    "session_id": session_id,
                    "conversation_history": session.get_recent_history(),
                    "last_user_input": session.last_user_input,
                },
            ),
        )

        logger.info("Requested strategic response generation")

    async def handle_response_ready(self, topic: str, message_data: Dict[str, Any]):
        """
        Handle response ready from Strategic Thinker.
        """
        session_id = message_data.get("data", {}).get(
            "session_id", self.active_session_id
        )
        if not session_id or session_id not in self.sessions:
            return

        session = self.sessions[session_id]
        response_content = message_data.get("data", {}).get("content")

        if response_content:
            # Update conversation history
            session.add_to_history("assistant", response_content)

            # Command Speaker to start TTS
            task_id = await self._start_speaker_task(session_id, response_content)
            session.current_speaker_task = task_id
            session.state = ConversationState.AI_SPEAKING

            await self._publish_status_update()
            logger.info("Started AI response")

    async def handle_response_completed(self, topic: str, message_data: Dict[str, Any]):
        """
        Handle response completion from Speaker.
        """
        session_id = message_data.get("data", {}).get(
            "session_id", self.active_session_id
        )
        if not session_id or session_id not in self.sessions:
            return

        session = self.sessions[session_id]

        # 只有在AI说话的状态下，才将状态变为空闲
        # 这样可以防止因response.cancel导致的意外状态变更
        if session.state == ConversationState.AI_SPEAKING:
            session.state = ConversationState.IDLE
            session.current_speaker_task = None

        # --- START OF FIX ---
        # 🎯 核心修复：删除下面这几行错误的代码。
        # AI语音播放完成不等于AI打断保护期结束。
        # 打断保护期的结束必须由 _resume_audio_input_after_delay 单独控制。
        #
        # await self._send_to_frontend(session_id, {
        #     "type": "interruption",
        #     "status": "end"
        # })
        # --- END OF FIX ---

        await self._publish_status_update()
        logger.info("AI response completed event handled.")

    async def _execute_interruption(
        self,
        session: Session,
        interruption_type: str,
        suggested_response: Optional[str],
        reason: str = "Unknown",
    ):
        """
        Execute the interruption sequence, handling both barge-in and proactive interruptions.
        """
        logger.info(f"Executing interruption: {interruption_type} - Reason: {reason}")

        # --- START OF FIX: 强制取消当前响应 ---
        # 这是最关键的一步：在请求新响应前，必须先取消任何可能正在进行的响应。
        from core.realtime_manager import get_global_manager

        realtime_manager = get_global_manager()
        if realtime_manager and realtime_manager.is_response_active:
            logger.warning(
                "🚨 Active response detected during interruption. Forcing cancellation..."
            )
            await realtime_manager.cancel_response()
            await asyncio.sleep(0.5)

        # 🎯 第三种方案：前端音频控制 - 立即通知前端停止录音
        audio_shield_activated = False
        if interruption_type != "barge_in" and suggested_response:
            # 1. 立即通知前端停止录音和清空音频缓冲区
            await self._send_to_frontend(
                session.session_id,
                {
                    "type": "audio_control",
                    "action": "stop_recording",
                    "reason": f"ai_interruption_{interruption_type}",
                    "duration": 5.0,  # 停止录音5秒
                    "message": "AI is providing guidance, please wait...",
                },
            )

            # 2. 设置音频护盾状态标志
            session.audio_shield_active = True
            session.audio_shield_reason = f"ai_interruption_{interruption_type}"
            audio_shield_activated = True

            logger.info(
                f"🛡️ Audio shield activated via frontend control for: {interruption_type}"
            )
        # --- END OF FRONTEND CONTROL APPROACH ---

        # 2. 立即通知前端进入打断状态 (对两种场景都适用)
        # 🎯 OPTIMIZATION: 减少保护期，提高响应性
        protection_period = (
            2.5 if interruption_type == "barge_in" else 4.0
        )  # 从3.0/5.0减少到2.5/4.0
        await self._send_to_frontend(
            session.session_id,
            {
                "type": "control_interruption",
                "status": "start",
                "interruption_type": interruption_type,
                "reason": reason,
                "duration": protection_period,
            },
        )

        # --- START OF FIX: 区分打断场景 ---
        # 场景A: 用户打断AI (Barge-in)。AI正在说话，需要先停下来。
        if session.state == ConversationState.AI_SPEAKING:
            logger.info("⚡️ Barge-in scenario detected. Stopping current AI speech.")
            self._change_state(
                session.session_id, ConversationState.INTERRUPTION_PENDING
            )

            # 如果AI被打断后需要回应，则记录意图，等待AI语音结束后触发
            if suggested_response:
                session.interruption_intent = {
                    "type": interruption_type,
                    "suggested_response": suggested_response,
                    "reason": reason,
                }
                logger.info(
                    f"Interruption response intent recorded: '{suggested_response}'"
                )

            # 命令Speaker停止当前说话 (这会触发 `AI_SPEECH_ENDED` 事件)
            await message_bus.publish(
                TopicNames.INTERRUPTION_COMMAND,
                create_message(
                    source_agent=self.agent_id,
                    event_type=EventType.SPEECH_INTERRUPTED,
                    message_type=MessageType.INTERRUPTION_SIGNAL,
                    data={"session_id": session.session_id, "reason": "barge_in"},
                ),
            )

        # 场景B: AI主动打断用户。AI是空闲的，可以直接开始说话。
        else:
            logger.info("🤖 Proactive interruption scenario. AI will speak directly.")

            # 🎯 新增：AI主动打断时也发送空音频来触发VAD
            # 这确保了用户当前的语音输入能够被正确处理和结束
            if realtime_manager and realtime_manager.is_connected:
                logger.info(
                    "🎯 AI interruption: Sending silence audio to trigger VAD for proper speech end detection"
                )
                try:
                    await realtime_manager.force_speech_end()
                    logger.info(
                        "✅ Silence audio sent successfully for AI interruption"
                    )
                except Exception as e:
                    logger.error(
                        f"❌ Failed to send silence audio during AI interruption: {e}"
                    )

            self._change_state(session.session_id, ConversationState.AI_SPEAKING)

            # 🎯 OPTIMIZATION: 立即命令 Speaker 使用新的上下文说出打断语，不再等待
            if suggested_response:
                # 并行发送TTS命令，减少延迟
                tts_task = asyncio.create_task(
                    message_bus.publish(
                        TopicNames.TTS_COMMANDS,
                        create_message(
                            source_agent=self.agent_id,
                            event_type=EventType.TTS_START,
                            message_type=MessageType.ORCHESTRATOR_COMMAND,
                            data={
                                "session_id": session.session_id,
                                "text": suggested_response,
                                "reason": reason,
                                "command": "speak_with_context",  # 使用上下文感知的命令
                                "priority": "high",  # 🎯 新增：高优先级标记
                            },
                        ),
                    )
                )
                session.add_to_history("assistant", suggested_response)
                logger.info(
                    f"🎤 Proactive interruption response sent to Speaker with HIGH priority: '{suggested_response}'"
                )

                # 等待TTS命令发送完成
                await tts_task
            else:
                # 如果没有建议回复，直接变为空闲
                self._change_state(session.session_id, ConversationState.IDLE)
        # --- END OF FIX ---

        # 3. 设置延迟任务来解除音频护盾 (对两种场景都适用)
        session.interruption_count += 1
        await self._publish_status_update()
        logger.info(f"⏳ Scheduling audio input resume in {protection_period} seconds.")
        asyncio.create_task(
            self._resume_audio_input_after_delay(protection_period, session.session_id)
        )

        # 🎯 第三种方案：前端控制方案会在保护期结束后自动恢复录音
        if audio_shield_activated:
            logger.info(
                f"🛡️ Frontend audio control will auto-recover after {protection_period}s protection period"
            )

        # 4. 发布事件用于日志/监控 (对两种场景都适用)
        await message_bus.publish(
            TopicNames.SESSION_EVENTS,
            create_message(
                source_agent=self.agent_id,
                event_type=EventType.INTERRUPTION_DECISION,
                message_type=MessageType.ORCHESTRATOR_STATUS,
                data={
                    "session_id": session.session_id,
                    "interruption_type": interruption_type,
                    "reason": reason,
                    "has_response": bool(suggested_response),
                    "timestamp": datetime.now().isoformat(),
                    "is_barge_in": interruption_type == "barge_in",
                    "fast_track": interruption_type == "barge_in",
                },
            ),
        )

    async def _start_speaker_task(self, session_id: str, text: str) -> str:
        """
        Start a Speaker task for TTS.

        Args:
            session_id: Session ID
            text: Text to speak

        Returns:
            Task ID
        """
        task_id = str(uuid.uuid4())

        await message_bus.publish(
            TopicNames.TTS_COMMANDS,
            create_message(
                source_agent=self.agent_id,
                event_type=EventType.TTS_START,
                message_type=MessageType.SPEAKER_COMMAND,
                data={
                    "task_id": task_id,
                    "session_id": session_id,
                    "text": text,
                    "command": "start_tts",
                },
            ),
        )

        logger.debug(f"Started speaker task: {task_id}")
        return task_id

    async def _stop_speaker_task(self, task_id: str):
        """
        Stop a Speaker task.

        Args:
            task_id: Task to stop
        """
        await message_bus.publish(
            TopicNames.TTS_COMMANDS,
            create_message(
                source_agent=self.agent_id,
                event_type=EventType.TTS_STOP,
                message_type=MessageType.SPEAKER_COMMAND,
                data={"task_id": task_id, "command": "stop_tts"},
            ),
        )

        logger.debug(f"Stopped speaker task: {task_id}")

    async def _stop_all_speaker_tasks(self):
        """
        Stop all active speaker tasks.
        """
        for session in self.sessions.values():
            if session.current_speaker_task:
                await self._stop_speaker_task(session.current_speaker_task)
                session.current_speaker_task = None

    async def _publish_status_update(self):
        """
        Publish current status to the system.
        """
        status_data = {
            "orchestrator_state": self.state.value,
            "active_session_id": self.active_session_id,
            "agent_states": {k: v.value for k, v in self.agent_states.items()},
            "session_count": len(self.sessions),
        }

        if self.active_session_id and self.active_session_id in self.sessions:
            session = self.sessions[self.active_session_id]
            status_data["conversation_state"] = session.state.value
            status_data["interruption_count"] = session.interruption_count

        await message_bus.publish(
            TopicNames.AGENT_STATUS,
            create_message(
                source_agent=self.agent_id,
                event_type=EventType.AGENT_STARTED,
                message_type=MessageType.ORCHESTRATOR_STATUS,
                data=status_data,
            ),
        )

    # 🎯 改造 _send_to_frontend 方法以使用直接通道
    async def _send_to_frontend(self, session_id: str, message_data: Dict[str, Any]):
        """
        发送消息到前端WebSocket，优先使用直接通道。
        """
        if session_id not in self.sessions:
            logger.warning(f"Cannot send to frontend, session {session_id} not found.")
            return

        session = self.sessions[session_id]

        # 🎯 如果存在直接回调，则使用它
        if session.send_callback:
            try:
                # 直接调用回调函数发送
                logger.info(
                    f"🚀 ORCHESTRATOR: Using direct channel to send {message_data.get('type')} to session {session_id}"
                )
                await session.send_callback(message_data)
                return
            except Exception as e:
                logger.error(
                    f"💥 Direct send callback failed for session {session_id}: {e}",
                    exc_info=True,
                )
                # 如果直接发送失败，可以考虑降级到消息总线，但通常直接失败意味着连接已断开

        # 降级到消息总线（作为备用方案）
        logger.warning(
            f"⚠️ No direct send callback for session {session_id}, falling back to message bus."
        )
        try:
            # 🎯 调试日志 1: 确认Orchestrator正在尝试发布消息
            logger.info(
                f"🔍 ORCHESTRATOR: Attempting to publish UI_UPDATE for session {session_id}. Data: {message_data}"
            )

            frontend_message = create_message(
                source_agent=self.agent_id,
                event_type=EventType.UI_UPDATE,
                message_type=MessageType.SYSTEM_NOTIFICATION,
                data={"session_id": session_id, **message_data},
            )
            await message_bus.publish(TopicNames.UI_UPDATE, frontend_message)

            # 🎯 调试日志 2: 确认成功发布到消息总线
            logger.info(
                f"✅ ORCHESTRATOR: Successfully published UI_UPDATE for session {session_id}. Message type: {message_data.get('type', 'unknown')}"
            )
        except Exception as e:
            # 🎯 调试日志 3: 捕获发布到MessageBus时的异常
            logger.error(
                f"💥 ORCHESTRATOR: Exception while publishing to message bus: {e}",
                exc_info=True,
            )

    async def _setup_subscriptions(self):
        """
        Set up message bus subscriptions.
        """
        # Subscribe to interruption decisions from Decider
        await message_bus.subscribe(
            TopicNames.INTERRUPTION_DECISION, self.handle_interruption_decision
        )

        # 🎯 **修复：** 只订阅 USER_SPEECH_STARTED 来触发打断
        await message_bus.subscribe(
            TopicNames.USER_SPEECH_STARTED, self.handle_user_speech_start_new
        )

        await message_bus.subscribe(
            TopicNames.USER_SPEECH_ENDED, self.handle_user_speech_end_new
        )

        # 🎯 **新增：** 订阅AI语音事件来管理状态，但不用它来触发打断
        await message_bus.subscribe(
            TopicNames.AI_SPEECH_STARTED, self.handle_ai_speech_start
        )

        await message_bus.subscribe(
            TopicNames.AI_SPEECH_ENDED, self.handle_ai_speech_end
        )

        # Subscribe to speech events from Listener (保持向后兼容)
        await message_bus.subscribe(
            TopicNames.SPEECH_EVENTS, self._handle_speech_events
        )

        # Subscribe to response events from Strategic Thinker
        await message_bus.subscribe(
            TopicNames.STRATEGIC_ANALYSIS, self._handle_strategic_events
        )

        # Subscribe to speaker completion events
        await message_bus.subscribe(
            TopicNames.RESPONSE_EVENTS, self._handle_response_events
        )

        logger.info("Orchestrator subscriptions set up")

    # 🎯 **新增：** 用于管理 AI 状态的处理器
    async def handle_ai_speech_start(self, topic: str, message: Dict[str, Any]):
        """处理AI开始说话事件，只用于状态管理，不触发打断"""
        session_id = message.get("data", {}).get("session_id")
        if session_id and session_id in self.sessions:
            self._change_state(session_id, ConversationState.AI_SPEAKING)
            self.logger.info(f"State changed to AI_SPEAKING for session {session_id}")

    async def handle_ai_speech_end(self, topic: str, message: Dict[str, Any]):
        """处理AI结束说话事件，现在是触发打断回应的唯一安全入口"""
        session_id = message.get("data", {}).get("session_id")
        if not session_id or session_id not in self.sessions:
            return

        session = self.sessions[session_id]

        # 🎯 核心逻辑：检查是否有待处理的打断回应
        # 这个逻辑现在是安全的，因为它只在 Qwen API 确认上一个 response 结束后才触发
        if session.interruption_intent:
            logger.info(
                f"✅ AI speech ended/cancelled. Now executing pending interruption intent for {session_id}."
            )

            intent = session.interruption_intent
            session.interruption_intent = None  # 清除待办

            # 使用新的有上下文的speak方法
            await message_bus.publish(
                TopicNames.TTS_COMMANDS,
                create_message(
                    source_agent=self.agent_id,
                    event_type=EventType.TTS_START,
                    message_type=MessageType.ORCHESTRATOR_COMMAND,
                    data={
                        "session_id": session_id,
                        "text": intent["suggested_response"],
                        "reason": intent["reason"],
                        "command": "speak_with_context",
                    },
                ),
            )
            self._change_state(session_id, ConversationState.AI_SPEAKING)
            session.add_to_history("assistant", intent["suggested_response"])
            logger.info(
                f"🎤 Contextual interruption response started: '{intent['suggested_response']}' (reason: {intent['reason']})"
            )

        # 正常结束流程
        elif session.state == ConversationState.AI_SPEAKING:
            self._change_state(session_id, ConversationState.IDLE)
            logger.info(
                f"State changed to IDLE for session {session_id} after AI finished speaking."
            )

    async def handle_user_speech_start_new(self, topic: str, message: Dict[str, Any]):
        """
        🎯 **新版本：** 处理明确的用户开始说话事件，实现快速barge-in
        """
        session_id = message.get("data", {}).get("session_id") or self.active_session_id
        if not session_id or session_id not in self.sessions:
            self.logger.warning("No active session for user speech")
            return

        session = self.sessions[session_id]

        # 快速通道逻辑：AI正在说话时用户插话，立即执行barge-in
        if session.state == ConversationState.AI_SPEAKING:
            self.logger.info(
                "⚡️ Barge-in detected! User started speaking while AI was responding. Executing immediate interruption."
            )
            session.state = ConversationState.INTERRUPTION_PENDING

            # 🎯 新增：切换到BARGE_IN VAD模式以优化打断检测
            vad_manager = get_vad_manager()
            if vad_manager:
                await vad_manager.switch_to_barge_in()
                self.logger.info(
                    "🎯 Switched to BARGE_IN VAD mode for optimized interruption detection"
                )

            # 直接执行打断，不等待Decider
            await self._execute_interruption(session, "barge_in", None, "User barge-in")

            self.logger.info(
                f"🎯 Fast-track barge-in executed for session {session_id}"
            )

        else:
            # 正常的用户开始说话流程
            session.state = ConversationState.USER_SPEAKING
            self.logger.info("User started speaking")

        await self._publish_status_update()

    async def handle_user_speech_end_new(self, topic: str, message: Dict[str, Any]):
        """
        🎯 **新版本：** 处理明确的用户结束说话事件
        """
        session_id = message.get("data", {}).get("session_id") or self.active_session_id
        if not session_id or session_id not in self.sessions:
            return

        session = self.sessions[session_id]

        if "transcript" in message.get("data", {}):
            session.last_user_input = message["data"]["transcript"]
            session.add_to_history("user", message["data"]["transcript"])

        # --- START OF FIX ---
        # 核心修复：移除检查 session.interruption_intent 的逻辑块
        # 因为打断现在是即时执行的

        # 正常流程
        session.state = ConversationState.AI_THINKING
        vad_manager = get_vad_manager()
        if vad_manager:
            await vad_manager.switch_to_standard()
            self.logger.info("🎯 Switched back to STANDARD VAD mode")

        self.logger.info("User finished speaking - AI thinking")
        await self._publish_status_update()
        # --- END OF FIX ---

    def _change_state(self, session_id: str, new_state: ConversationState):
        """帮助方法：改变会话状态"""
        if session_id in self.sessions:
            old_state = self.sessions[session_id].state
            self.sessions[session_id].state = new_state
            self.logger.debug(f"Session {session_id}: {old_state} -> {new_state}")

    async def _resume_audio_input_after_delay(
        self, delay_seconds: float, session_id: str
    ):
        """
        在指定延迟后恢复音频输入监听, 并通知前端
        """
        try:
            await asyncio.sleep(delay_seconds)

            # 🎯 第三种方案：通知前端恢复录音
            await self._send_to_frontend(
                session_id,
                {
                    "type": "audio_control",
                    "action": "resume_recording",
                    "message": "You can speak now",
                },
            )

            # 清除音频护盾状态
            if session_id in self.sessions:
                session = self.sessions[session_id]
                session.audio_shield_active = False
                session.audio_shield_reason = None

            logger.info(f"✅ Frontend audio control resumed for session {session_id}")

            # 通知前端打断状态结束
            await self._send_to_frontend(
                session_id, {"type": "control_interruption", "status": "end"}
            )

        except Exception as e:
            logger.error(f"Error resuming audio input after delay: {e}")

    async def _restore_vad_auto_response_after_delay(self, delay_seconds: float):
        """
        在指定延迟后恢复VAD的自动响应功能。
        """
        try:
            await asyncio.sleep(delay_seconds)
            vad_manager = get_vad_manager()
            if vad_manager:
                await vad_manager.restore_auto_response()
                logger.info("🎯 VAD auto-response restored.")
        except Exception as e:
            logger.error(f"Error restoring VAD auto-response after delay: {e}")

    async def _handle_speech_events(self, topic: str, message_data: Dict[str, Any]):
        """Handle speech-related events."""
        event_type = message_data.get("event_type")
        data = message_data.get("data", {})

        if event_type == EventType.SPEECH_STARTED.value:
            await self.handle_user_speech_start(data)
        elif event_type == EventType.SPEECH_STOPPED.value:
            await self.handle_user_speech_end(data)

    async def _handle_strategic_events(self, topic: str, message_data: Dict[str, Any]):
        """Handle strategic thinking events."""
        event_type = message_data.get("event_type")

        if event_type == EventType.STRATEGIC_ANALYSIS.value:
            await self.handle_response_ready(topic, message_data)
        elif event_type == EventType.RESPONSE_REQUIRED.value:
            await self.handle_response_required(topic, message_data)

    async def _handle_response_events(self, topic: str, message_data: Dict[str, Any]):
        """Handle response events from Speaker."""
        event_type = message_data.get("event_type")

        if event_type == EventType.RESPONSE_COMPLETED.value:
            await self.handle_response_completed(topic, message_data)

    def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get status of a specific session.

        Args:
            session_id: Session ID to query

        Returns:
            Session status data or None if not found
        """
        if session_id not in self.sessions:
            return None

        session = self.sessions[session_id]
        return {
            "session_id": session.session_id,
            "created_at": session.created_at.isoformat(),
            "state": session.state.value,
            "conversation_length": len(session.conversation_history),
            "interruption_count": session.interruption_count,
            "current_speaker_task": session.current_speaker_task,
            "last_user_input": session.last_user_input,
        }

    async def get_agent_status(self) -> Dict[str, Any]:
        """
        Get comprehensive status of the Orchestrator agent.

        Returns:
            Dict containing agent status and session information
        """
        base_status = await super().get_agent_status()

        # Add orchestrator-specific status
        orchestrator_status = {
            **base_status,
            "active_session_id": self.active_session_id,
            "total_sessions": len(self.sessions),
            "agent_states": self.agent_states.copy(),
            "message_routes": self.message_routes.copy(),
        }

        # Add active session status if available
        if self.active_session_id and self.active_session_id in self.sessions:
            orchestrator_status["active_session"] = self.get_session_status(
                self.active_session_id
            )

        return orchestrator_status
